import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  TextField,
  IconButton,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { useState } from 'react';

// ----------------------------------------------------------------------

interface TeamMember {
  id: string;
  name: string;
  avatar: string;
  role?: string;
}

interface TeamMembersDialogProps {
  open: boolean;
  onClose: () => void;
  teamName: string;
  members: TeamMember[];
}

export function TeamMembersDialog({ open, onClose, teamName, members }: TeamMembersDialogProps) {
  const [searchQuery, setSearchQuery] = useState('');

  // Filter members based on search query
  const filteredMembers = members.filter((member) =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          
          borderRadius: 4,
          maxHeight: '80vh',
          p: 3,
          position: 'relative',
          width: { xs: '90%', sm: '450px' },
          mx: 'auto',
        },
      }}
    >
      {/* Close button */}
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          right: 12,
          top: 12,
          color: 'grey.500',
          bgcolor: 'grey.100',
          '&:hover': {
            bgcolor: 'grey.200',
          },
          width: 30,
          height: 30,
        }}
      >
        <Iconify icon="eva:close-fill" width={16} height={16} />
      </IconButton>

      {/* Dialog Title */}
      <DialogTitle sx={{ textAlign: 'center', p: 1, pb: 0 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
          Group Members
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Browse the members of the team you are working with
        </Typography>
      </DialogTitle>

      {/* Members List */}
      <DialogContent sx={{ p: 0, px: 2, maxHeight: 300, overflowY: 'auto' }}>
        <List sx={{ pt: 0 }}>
          {filteredMembers.map((member) => (
            <ListItem
              key={member.id}
              sx={{
                py: 1.5,
                borderBottom: '1px solid',
                borderColor: 'grey.100',
                '&:last-child': {
                  borderBottom: 'none',
                },
                px: 0,
              }}
            >
              <ListItemAvatar>
                <Avatar src={member.avatar} alt={member.name} />
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                    DataAnalyzer
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Social Media
                  </Typography>
                }
              />
            </ListItem>
          ))}
        </List>
      </DialogContent>

      {/* Close Button */}
      <DialogActions sx={{ p: 2, pt: 1 }}>
        <Button
          onClick={onClose}
          fullWidth
          variant="outlined"
          sx={{
            borderRadius: 1,
            py: 1.5,
            textTransform: 'none',
            fontWeight: 500,
            border: '1px solid rgba(0, 0, 0, 0.12)',
            color: 'text.primary',
            '&:hover': {
              bgcolor: 'grey.100',
              borderColor: 'grey.300',
            },
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}
