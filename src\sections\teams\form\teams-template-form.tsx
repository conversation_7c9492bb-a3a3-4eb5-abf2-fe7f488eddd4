import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>per,
  Step,
  StepLabel,
  TextField,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Card,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Avatar,
  IconButton,
  InputAdornment,
  Stack,
  Checkbox,
  Grid,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { Icon } from '@iconify/react';
import { useRouter } from 'src/routes/hooks';
import { AppButton, AppContainer } from 'src/components/common';
import { Iconify } from 'src/components/iconify';
import { Field, Form } from 'src/components/hook-form';
import { paths } from 'src/routes/paths';
import useTeamTemplateForm from './use-team-template-form';

// Custom styled components to match the design - outlined circles with no connecting lines
const CustomStepIcon = styled('div')<{ active?: boolean; completed?: boolean }>(
  ({ active, completed }) => ({
    width: 32,
    height: 32,
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '0.875rem',
    fontWeight: 600,
    backgroundColor: active ? '#8B5CF6' : completed ? '#10B981' : '#FFFFFF',
    color: active ? '#FFFFFF' : completed ? '#FFFFFF' : '#9CA3AF',
    border: active ? 'none' : completed ? 'none' : '2px solid #E5E7EB',
    transition: 'all 0.2s ease-in-out',
    boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  })
);

// Custom styled chip-like step container
const StepChipContainer = styled(Box)<{ active?: boolean; completed?: boolean }>(
  ({ active, completed }) => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: '12px 16px',
    borderRadius: '24px',
    backgroundColor: active ? 'rgba(163, 139, 233, 0.13)' : 'transparent',
    border: active ? '1px solid rgba(163, 139, 233, 0.3)' : '1px solid transparent',
    transition: 'all 0.2s ease-in-out',
    minWidth: '120px',
  })
);

const TeamsTemplateForm = () => {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState(0);
  const [teamName, setTeamName] = useState('');
  const [description, setDescription] = useState('');
  const [flowControl, setFlowControl] = useState('auto');
  const [membersExpanded, setMembersExpanded] = useState(true);
  const [toolsExpanded, setToolsExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { methods, FREQUENCY_OPTIONS } = useTeamTemplateForm();
  // Configuration state
  const [configExpanded, setConfigExpanded] = useState({
    gmail: false,
    facebook: false,
    pdf: false,
    x: true, // X (Twitter) is expanded in the image
  });
  const [apiKeys, setApiKeys] = useState({
    gmail: { value: '', status: 'linked' }, // linked, failed, or null
    facebook: { value: '', status: 'linked' },
    pdf: { value: '', status: 'linked' },
    x: { value: '', status: 'failed' },
  });

  // Sample members data
  const [members] = useState([
    { id: 1, name: 'Layla Al-Farsi', avatar: '/assets/images/avatar/avatar-1.jpg', added: false },
    { id: 2, name: 'Omar Al-Hakim', avatar: '/assets/images/avatar/avatar-2.jpg', added: true },
    { id: 3, name: 'Zayd Al-Mansoori', avatar: '/assets/images/avatar/avatar-3.jpg', added: true },
  ]);

  // Configuration tools data
  const configTools = [
    {
      id: 'gmail',
      name: 'Get emails from Gmail',
      icon: 'logos:google-gmail',
      color: '#EA4335',
      status: apiKeys.gmail.status,
    },
    {
      id: 'facebook',
      name: 'Get the posts of a Facebook page',
      icon: 'logos:facebook',
      color: '#1877F2',
      status: apiKeys.facebook.status,
    },
    {
      id: 'pdf',
      name: 'Summarize a PDF file',
      icon: 'vscode-icons:file-type-pdf2',
      color: '#DC2626',
      status: apiKeys.pdf.status,
    },
    {
      id: 'x',
      name: 'Get the posts of a X account',
      icon: 'ri:twitter-x-fill',
      color: '#000000',
      status: apiKeys.x.status,
    },
  ];

  const steps = [
    'Team Information',
    'Tools & Members',
    'Configuration',
    'Instructions',
    'Frequency',
  ];

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleCancel = () => {
    router.push(paths.dashboard.teams.chat);
  };

  // Configuration handlers
  const handleConfigToggle = (toolId: string) => {
    setConfigExpanded((prev) => ({
      ...prev,
      [toolId]: !prev[toolId as keyof typeof prev],
    }));
  };

  const handleApiKeyChange = (toolId: string, value: string) => {
    setApiKeys((prev) => ({
      ...prev,
      [toolId]: { ...prev[toolId as keyof typeof prev], value },
    }));
  };

  const handleSaveApiKey = (toolId: string) => {
    const apiKey = apiKeys[toolId as keyof typeof apiKeys].value;
    if (apiKey.trim()) {
      // Simulate API call - randomly set success or failure for demo
      const isSuccess = Math.random() > 0.3;
      setApiKeys((prev) => ({
        ...prev,
        [toolId]: {
          ...prev[toolId as keyof typeof prev],
          status: isSuccess ? 'linked' : 'failed',
        },
      }));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'linked':
        return '#10B981';
      case 'failed':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'linked':
        return 'Linked';
      case 'failed':
        return 'Failed';
      default:
        return '';
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h5" fontWeight={700} mb={1} color="#1F2937">
              Team Information
            </Typography>
            <Typography variant="body2" color="#6B7280" mb={1} fontSize="0.875rem">
              Create your own team and view your team members and tools
            </Typography>
            <Card
              sx={{
                background: 'white',
                padding: '20px',
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
              }}
            >
              <Box sx={{}}>
                <Typography variant="body2" fontWeight={500} color="#374151">
                  Team Name
                </Typography>
                <Field.Text name="name" fullWidth placeholder="Type your team name" />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight={500} color="#374151">
                  Description
                </Typography>
                <Field.Text
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      height: '100px',
                    },
                  }}
                  name="description"
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Type your team description"
                />
              </Box>

              <Box sx={{}}>
                <Typography variant="body2" fontWeight={500} color="#374151">
                  Flow Control
                </Typography>
                <FormControl>
                  <RadioGroup
                    row
                    value={flowControl}
                    onChange={(e) => setFlowControl(e.target.value)}
                    sx={{ gap: 4 }}
                  >
                    <FormControlLabel
                      value="auto"
                      control={
                        <Radio
                          sx={{
                            color: '#D1D5DB',
                            '&.Mui-checked': {
                              color: '#8B5CF6',
                            },
                            '& .MuiSvgIcon-root': {
                              fontSize: 20,
                            },
                          }}
                        />
                      }
                      label={
                        <Typography
                          variant="body2"
                          sx={{ fontSize: '0.875rem', color: '#374151', fontWeight: 500 }}
                        >
                          Auto
                        </Typography>
                      }
                    />
                    <FormControlLabel
                      value="manual"
                      control={
                        <Radio
                          sx={{
                            color: '#D1D5DB',
                            '&.Mui-checked': {
                              color: '#8B5CF6',
                            },
                            '& .MuiSvgIcon-root': {
                              fontSize: 20,
                            },
                          }}
                        />
                      }
                      label={
                        <Typography
                          variant="body2"
                          sx={{ fontSize: '0.875rem', color: '#374151', fontWeight: 500 }}
                        >
                          Manual
                        </Typography>
                      }
                    />
                  </RadioGroup>
                </FormControl>
              </Box>
            </Card>
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="h5" fontWeight={700} mb={1} color="#1F2937">
              Tools & Members
            </Typography>
            <Typography variant="body2" color="#6B7280" mb={4} fontSize="0.875rem">
              Add team members and tools
            </Typography>

            {/* Members Accordion */}
            <Accordion
              expanded={membersExpanded}
              onChange={() => setMembersExpanded(!membersExpanded)}
              sx={{
                mb: 3,
                boxShadow: 'none',
                border: '1px solid #E5E7EB',
                borderRadius: '8px !important',
                '&:before': {
                  display: 'none',
                },
              }}
            >
              <AccordionSummary
                expandIcon={<Icon icon="eva:chevron-down-fill" />}
                sx={{
                  backgroundColor: '#F9FAFB',
                  borderRadius: '8px',
                  '& .MuiAccordionSummary-content': {
                    margin: '12px 0',
                  },
                }}
              >
                <Typography variant="h6" fontWeight={600} color="#374151">
                  Members
                </Typography>
              </AccordionSummary>
              <AccordionDetails sx={{ p: 3 }}>
                {/* Search Field */}
                <Card
                  sx={{
                    background: (theme) => theme.palette.background.neutral,
                    padding: '20px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                  }}
                >
                  <TextField
                    fullWidth
                    placeholder="Search"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Icon icon="eva:search-fill" color="#9CA3AF" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      mb: 3,
                      '& .MuiOutlinedInput-root': {
                        bgcolor: '#F9FAFB',
                        borderRadius: '8px',
                        '& fieldset': {
                          borderColor: '#E5E7EB',
                        },
                        '&:hover fieldset': {
                          borderColor: '#D1D5DB',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#8B5CF6',
                        },
                      },
                      background: '#F1EFF3',
                    }}
                  />

                  {/* Members List */}
                  <Box>
                    {members.map((member) => (
                      <Box
                        key={member.id}
                        display="flex"
                        alignItems="center"
                        justifyContent="space-between"
                        py={2}
                        borderBottom="1px solid #F3F4F6"
                        sx={{
                          background: '#F1EFF3',
                          mt: '8px',
                          borderRadius: '16px',
                          padding: '8px',
                        }}
                      >
                        <Box display="flex" alignItems="center" gap={2}>
                          <Avatar src={member.avatar} sx={{ width: 40, height: 40 }} />
                          <Typography variant="body2" fontWeight={500} color="#374151">
                            {member.name}
                          </Typography>
                        </Box>
                        <IconButton
                          size="small"
                          sx={{
                            width: 32,
                            height: 32,
                            borderRadius: '8px',
                            border: '1px solid',
                            borderColor: member.added ? '#F87171' : '#8B5CF6',
                            backgroundColor: member.added ? '#FEF2F2' : '#F3E8FF',
                            color: member.added ? '#F87171' : '#8B5CF6',
                            '&:hover': {
                              backgroundColor: member.added ? '#FEE2E2' : '#EDE9FE',
                            },
                          }}
                        >
                          <Icon
                            icon={member.added ? 'eva:minus-fill' : 'eva:plus-fill'}
                            width={16}
                          />
                        </IconButton>
                      </Box>
                    ))}
                  </Box>
                </Card>
              </AccordionDetails>
            </Accordion>

            {/* Tools Accordion */}
            <Accordion
              expanded={toolsExpanded}
              onChange={() => setToolsExpanded(!toolsExpanded)}
              sx={{
                boxShadow: 'none',
                border: '1px solid #E5E7EB',
                borderRadius: '8px !important',
                '&:before': {
                  display: 'none',
                },
              }}
            >
              <AccordionSummary
                expandIcon={<Icon icon="eva:chevron-down-fill" />}
                sx={{
                  backgroundColor: '#F9FAFB',
                  borderRadius: '8px',
                  '& .MuiAccordionSummary-content': {
                    margin: '12px 0',
                  },
                }}
              >
                <Typography variant="h6" fontWeight={600} color="#374151">
                  Tools
                </Typography>
              </AccordionSummary>
              <AccordionDetails sx={{ p: 3 }}>
                <Card
                  sx={{
                    background: (theme) => theme.palette.background.neutral,
                    padding: '20px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                  }}
                >
                  {/* Search Field */}
                  <TextField
                    fullWidth
                    placeholder="Search"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Icon icon="eva:search-fill" color="#9CA3AF" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      mb: 3,
                      '& .MuiOutlinedInput-root': {
                        bgcolor: '#F9FAFB',
                        borderRadius: '8px',
                        '& fieldset': {
                          borderColor: '#E5E7EB',
                        },
                        '&:hover fieldset': {
                          borderColor: '#D1D5DB',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#8B5CF6',
                        },
                      },
                      background: '#F1EFF3',
                    }}
                  />
                  <Stack spacing={2}>
                    {['Gmail', 'Facebook', 'LinkedIn'].map((tool) => (
                      <Card key={tool} variant="outlined" sx={{ background: '#F1EFF3', p: 2 }}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Checkbox />
                          <Iconify
                            icon={
                              tool === 'Gmail'
                                ? 'logos:google-gmail'
                                : tool === 'Facebook'
                                  ? 'logos:facebook'
                                  : 'logos:linkedin-icon'
                            }
                            width={24}
                            height={24}
                          />
                          <Typography variant="subtitle2">{tool}</Typography>
                        </Stack>
                      </Card>
                    ))}
                  </Stack>
                </Card>
              </AccordionDetails>
            </Accordion>
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="h5" fontWeight={700} mb={1} color="#1F2937">
              Configuration
            </Typography>
            <Typography variant="body2" color="#6B7280" mb={1} fontSize="0.875rem">
              The tools that your team can require some info.
            </Typography>

            {/* Configuration Tools */}
            <Card sx={{ p: '20px' }}>
              {configTools.map((tool) => (
                <Accordion
                  key={tool.id}
                  expanded={configExpanded[tool.id as keyof typeof configExpanded]}
                  onChange={() => handleConfigToggle(tool.id)}
                  sx={{
                    mb: 2,
                    boxShadow: 'none',
                    border: '1px solid #E5E7EB',
                    borderRadius: '8px !important',
                    '&:before': {
                      display: 'none',
                    },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<Icon icon="eva:chevron-down-fill" />}
                    sx={{
                      background: (theme) => theme.palette.background.neutral,
                      borderRadius: '8px',
                      '& .MuiAccordionSummary-content': {
                        margin: '12px 0',
                        alignItems: 'center',
                      },
                    }}
                  >
                    <Box display="flex" alignItems="center" gap={2} flex={1}>
                      <Icon icon={tool.icon} width={24} height={24} />
                      <Typography variant="body2" fontWeight={500} color="#374151">
                        {tool.name}
                      </Typography>
                      {tool.status && (
                        <Chip
                          label={getStatusText(tool.status)}
                          size="small"
                          sx={{
                            backgroundColor: tool.status === 'linked' ? '#DCFCE7' : '#FEE2E2',
                            color: getStatusColor(tool.status),
                            fontWeight: 600,
                            fontSize: '0.75rem',
                            height: '24px',
                            '& .MuiChip-label': {
                              px: 1,
                            },
                          }}
                        />
                      )}
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails
                    sx={{ p: 3, pt: 0, background: (theme) => theme.palette.background.neutral }}
                  >
                    <Box>
                      <Box display="flex" gap={2}>
                        <Field.Text fullWidth placeholder="API Key" name="apiKey" type="password" />
                        <AppButton
                          size="small"
                          variant="contained"
                          onClick={() => handleSaveApiKey(tool.id)}
                          sx={{
                            bgcolor: '#8B5CF6',
                            color: '#FFFFFF',
                            fontSize: '0.875rem',
                            fontWeight: 600,
                            textTransform: 'none',
                            borderRadius: '8px',
                            height: '32px',
                            width: '64px',
                            boxShadow: 'none',
                            '&:hover': {
                              bgcolor: '#7C3AED',
                              boxShadow: 'none',
                            },
                          }}
                          label="Save"
                        />
                      </Box>
                    </Box>
                  </AccordionDetails>
                </Accordion>
              ))}
            </Card>
          </Box>
        );
      case 3:
        return (
          <Box>
            <Typography variant="h5" fontWeight={700} mb={1} color="#1F2937">
              Instructions
            </Typography>
            <Typography variant="body2" color="#6B7280" mb={1} fontSize="0.875rem">
              Please provide your team with your instructions
            </Typography>
            <Card sx={{ padding: '20px' }}>
              <Field.Instruction name="instructions" label="" rows={6} />
            </Card>
          </Box>
        );
      case 4:
        return (
          <Box>
            <Typography variant="h5" fontWeight={700} mb={1} color="#1F2937">
              Frequency
            </Typography>
            <Typography variant="body2" color="#6B7280" mb={1} fontSize="0.875rem">
              Please select the frequency at which the team should operate
            </Typography>
            <Card sx={{ padding: '20px' }}>
              <Grid container sx={{ mt: '14px' }} spacing={2}>
                <Grid item xs={12} md={6}>
                  <Field.Select name="frequency" label="frequency" options={FREQUENCY_OPTIONS} />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Field.DatePicker name="startDate" label="start date" />
                </Grid>
              </Grid>
            </Card>
          </Box>
        );
      default:
        return (
          <Box textAlign="center" py={4}>
            <Typography variant="h6" color="text.secondary">
              Step {step + 1} content will be implemented here
            </Typography>
          </Box>
        );
    }
  };

  return (
    <AppContainer title="Create Team" routeLinks={[{ name: 'Teams' }, { name: 'Create Team' }]}>
      {/* Stepper */}
      <Box sx={{ border: (theme) => `0.5px solid  ${theme.palette.divider}`, mt: '12px' }} />
      <Stepper
        activeStep={activeStep}
        alternativeLabel
        connector={null}
        sx={{
          mt: '12px',
          mb: 6,
          '& .MuiStepLabel-root': {
            padding: 0,
          },
          '& .MuiStepLabel-labelContainer': {
            marginTop: 2,
          },
          '& .MuiStepConnector-root': {
            display: 'none',
          },
        }}
      >
        {steps.map((label, index) => (
          <Step key={label}>
            <StepLabel
              StepIconComponent={({ active, completed }) => (
                <StepChipContainer active={active} completed={completed}>
                  <CustomStepIcon active={active} completed={completed}>
                    {completed ? <Icon icon="eva:checkmark-fill" width={20} /> : index + 1}
                  </CustomStepIcon>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: active ? 600 : 500,
                      color: active ? '#1F2937' : completed ? '#10B981' : '#9CA3AF',
                      fontSize: '0.875rem',
                      marginTop: '8px',
                      textAlign: 'center',
                    }}
                  >
                    {label}
                  </Typography>
                </StepChipContainer>
              )}
              sx={{
                '& .MuiStepLabel-label': {
                  display: 'none', // Hide default label since we're using custom one
                },
              }}
            >
              {label}
            </StepLabel>
          </Step>
        ))}
      </Stepper>

      {/* Step Content */}
      <Box
        sx={{
          background: (theme) => theme.palette.background.neutral,
          padding: '20px',
          borderRadius: '16px',
        }}
      >
        <Form
          methods={methods}
          onSubmit={(_event?: React.FormEvent<HTMLFormElement>) => {
            // Always prevent default form submission
            // We'll handle submission manually with the Create Team button
            if (_event) _event.preventDefault();
          }}
        >
          <Box sx={{ mb: '16px' }}>{renderStepContent(activeStep)}</Box>
        </Form>

        {/* Action Buttons */}
        <Box display="flex" gap={3}>
          <AppButton
            size="small"
            variant="outlined"
            onClick={handleCancel}
            sx={{
              flex: 1,
              py: 2,
              borderColor: '#D1D5DB',
              color: '#8B5CF6',
              fontSize: '0.875rem',
              fontWeight: 600,
              textTransform: 'none',
              borderRadius: '8px',

              '&:hover': {
                borderColor: '#8B5CF6',
                bgcolor: 'rgba(139, 92, 246, 0.04)',
              },
            }}
            label="Cancel"
          />

          <AppButton
            size="small"
            variant="contained"
            onClick={activeStep === steps.length - 1 ? handleCancel : handleNext}
            sx={{
              flex: 1,
              py: 2,
              bgcolor: '#8B5CF6',
              fontSize: '0.875rem',
              fontWeight: 600,
              textTransform: 'none',
              borderRadius: '8px',

              boxShadow: 'none',
              '&:hover': {
                bgcolor: '#7C3AED',
                boxShadow: 'none',
              },
            }}
            label={activeStep === steps.length - 1 ? 'Create Team' : 'Next'}
          />
        </Box>
      </Box>
    </AppContainer>
  );
};

export default TeamsTemplateForm;
